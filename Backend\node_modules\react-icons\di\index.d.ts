// THIS FILE IS AUTO GENERATED
import type { IconType } from '../lib/index'
export declare const DiAndroid: IconType;
export declare const DiAngularSimple: IconType;
export declare const DiAppcelerator: IconType;
export declare const DiApple: IconType;
export declare const DiAppstore: IconType;
export declare const DiAptana: IconType;
export declare const DiAsterisk: IconType;
export declare const DiAtlassian: IconType;
export declare const DiAtom: IconType;
export declare const DiAws: IconType;
export declare const DiBackbone: IconType;
export declare const DiBingSmall: IconType;
export declare const DiBintray: IconType;
export declare const DiBitbucket: IconType;
export declare const DiBlackberry: IconType;
export declare const DiBootstrap: IconType;
export declare const DiBower: IconType;
export declare const DiBrackets: IconType;
export declare const DiBugsense: IconType;
export declare const DiCelluloid: IconType;
export declare const DiChrome: IconType;
export declare const DiCisco: IconType;
export declare const DiClojureAlt: IconType;
export declare const DiClojure: IconType;
export declare const DiCloud9: IconType;
export declare const DiCoda: IconType;
export declare const DiCodeBadge: IconType;
export declare const DiCode: IconType;
export declare const DiCodeigniter: IconType;
export declare const DiCodepen: IconType;
export declare const DiCodrops: IconType;
export declare const DiCoffeescript: IconType;
export declare const DiCompass: IconType;
export declare const DiComposer: IconType;
export declare const DiCreativecommonsBadge: IconType;
export declare const DiCreativecommons: IconType;
export declare const DiCssTricks: IconType;
export declare const DiCss3Full: IconType;
export declare const DiCss3: IconType;
export declare const DiCssdeck: IconType;
export declare const DiDart: IconType;
export declare const DiDatabase: IconType;
export declare const DiDebian: IconType;
export declare const DiDigitalOcean: IconType;
export declare const DiDjango: IconType;
export declare const DiDlang: IconType;
export declare const DiDocker: IconType;
export declare const DiDoctrine: IconType;
export declare const DiDojo: IconType;
export declare const DiDotnet: IconType;
export declare const DiDreamweaver: IconType;
export declare const DiDropbox: IconType;
export declare const DiDrupal: IconType;
export declare const DiEclipse: IconType;
export declare const DiEmber: IconType;
export declare const DiEnvato: IconType;
export declare const DiErlang: IconType;
export declare const DiExtjs: IconType;
export declare const DiFirebase: IconType;
export declare const DiFirefox: IconType;
export declare const DiFsharp: IconType;
export declare const DiGhostSmall: IconType;
export declare const DiGhost: IconType;
export declare const DiGitBranch: IconType;
export declare const DiGitCommit: IconType;
export declare const DiGitCompare: IconType;
export declare const DiGitMerge: IconType;
export declare const DiGitPullRequest: IconType;
export declare const DiGit: IconType;
export declare const DiGithubAlt: IconType;
export declare const DiGithubBadge: IconType;
export declare const DiGithubFull: IconType;
export declare const DiGithub: IconType;
export declare const DiGnu: IconType;
export declare const DiGo: IconType;
export declare const DiGoogleAnalytics: IconType;
export declare const DiGoogleDrive: IconType;
export declare const DiGoogleCloudPlatform: IconType;
export declare const DiGrails: IconType;
export declare const DiGroovy: IconType;
export declare const DiGrunt: IconType;
export declare const DiGulp: IconType;
export declare const DiHackernews: IconType;
export declare const DiHaskell: IconType;
export declare const DiHeroku: IconType;
export declare const DiHtml53dEffects: IconType;
export declare const DiHtml5Connectivity: IconType;
export declare const DiHtml5DeviceAccess: IconType;
export declare const DiHtml5Multimedia: IconType;
export declare const DiHtml5: IconType;
export declare const DiIe: IconType;
export declare const DiIllustrator: IconType;
export declare const DiIntellij: IconType;
export declare const DiIonic: IconType;
export declare const DiJava: IconType;
export declare const DiJavascript1: IconType;
export declare const DiJavascript: IconType;
export declare const DiJekyllSmall: IconType;
export declare const DiJenkins: IconType;
export declare const DiJira: IconType;
export declare const DiJoomla: IconType;
export declare const DiJqueryLogo: IconType;
export declare const DiJqueryUiLogo: IconType;
export declare const DiJsBadge: IconType;
export declare const DiKomodo: IconType;
export declare const DiKrakenjsBadge: IconType;
export declare const DiKrakenjs: IconType;
export declare const DiLaravel: IconType;
export declare const DiLess: IconType;
export declare const DiLinux: IconType;
export declare const DiMagento: IconType;
export declare const DiMailchimp: IconType;
export declare const DiMarkdown: IconType;
export declare const DiMaterializecss: IconType;
export declare const DiMeteor: IconType;
export declare const DiMeteorfull: IconType;
export declare const DiMitlicence: IconType;
export declare const DiModernizr: IconType;
export declare const DiMongodb: IconType;
export declare const DiMootoolsBadge: IconType;
export declare const DiMootools: IconType;
export declare const DiMozilla: IconType;
export declare const DiMsqlServer: IconType;
export declare const DiMysql: IconType;
export declare const DiNancy: IconType;
export declare const DiNetbeans: IconType;
export declare const DiNetmagazine: IconType;
export declare const DiNginx: IconType;
export declare const DiNodejsSmall: IconType;
export declare const DiNodejs: IconType;
export declare const DiNpm: IconType;
export declare const DiOnedrive: IconType;
export declare const DiOpenshift: IconType;
export declare const DiOpensource: IconType;
export declare const DiOpera: IconType;
export declare const DiPerl: IconType;
export declare const DiPhonegap: IconType;
export declare const DiPhotoshop: IconType;
export declare const DiPhp: IconType;
export declare const DiPostgresql: IconType;
export declare const DiProlog: IconType;
export declare const DiPython: IconType;
export declare const DiRackspace: IconType;
export declare const DiRaphael: IconType;
export declare const DiRasberryPi: IconType;
export declare const DiReact: IconType;
export declare const DiRedhat: IconType;
export declare const DiRedis: IconType;
export declare const DiRequirejs: IconType;
export declare const DiResponsive: IconType;
export declare const DiRor: IconType;
export declare const DiRubyRough: IconType;
export declare const DiRuby: IconType;
export declare const DiRust: IconType;
export declare const DiSafari: IconType;
export declare const DiSass: IconType;
export declare const DiScala: IconType;
export declare const DiScriptcs: IconType;
export declare const DiScrum: IconType;
export declare const DiSenchatouch: IconType;
export declare const DiSizzlejs: IconType;
export declare const DiSmashingMagazine: IconType;
export declare const DiSnapSvg: IconType;
export declare const DiSpark: IconType;
export declare const DiSqllite: IconType;
export declare const DiStackoverflow: IconType;
export declare const DiStreamline: IconType;
export declare const DiStylus: IconType;
export declare const DiSublime: IconType;
export declare const DiSwift: IconType;
export declare const DiSymfonyBadge: IconType;
export declare const DiSymfony: IconType;
export declare const DiTechcrunch: IconType;
export declare const DiTerminalBadge: IconType;
export declare const DiTerminal: IconType;
export declare const DiTravis: IconType;
export declare const DiTrello: IconType;
export declare const DiTypo3: IconType;
export declare const DiUbuntu: IconType;
export declare const DiUikit: IconType;
export declare const DiUnitySmall: IconType;
export declare const DiVim: IconType;
export declare const DiVisualstudio: IconType;
export declare const DiW3C: IconType;
export declare const DiWebplatform: IconType;
export declare const DiWindows: IconType;
export declare const DiWordpress: IconType;
export declare const DiYahooSmall: IconType;
export declare const DiYahoo: IconType;
export declare const DiYeoman: IconType;
export declare const DiYii: IconType;
export declare const DiZend: IconType;

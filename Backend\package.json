{"name": "backend", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "create-superadmin": "node scripts/createSuperAdmin.js", "seed-superadmins": "node scripts/createSuperAdmin.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.10.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.6.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1", "nodemailer": "^7.0.4", "react-icons": "^5.5.0"}, "devDependencies": {"nodemon": "^3.1.10"}}
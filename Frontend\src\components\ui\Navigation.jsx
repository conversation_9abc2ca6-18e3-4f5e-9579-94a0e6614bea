import { useState } from "react"
import { useNavigate, useLocation } from "react-router-dom"
import { useAuth } from "../../contexts/AuthContext"
import { useTheme } from "../../contexts/ThemeContext"
import { Button } from "./Button"
import { ThemeToggle } from "./ThemeToggle"
import { Avatar } from "./Avatar"
import { Badge } from "./Badge"
import { Icon } from "./Icons"
import { cn } from "../../lib/utils"

export function Navigation({ title, subtitle, actions = [] }) {
  const { user, logout } = useAuth()
  const navigate = useNavigate()
  const location = useLocation()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  
  const getRoleColor = (role) => {
    switch (role) {
      case "superadmin":
        return "primary"
      case "admin":
        return "warning"
      case "employee":
        return "success"
      default:
        return "default"
    }
  }
  
  const getHomeRoute = () => {
    switch (user?.role) {
      case "superadmin":
        return "/superadmin/dashboard"
      case "admin":
        return "/admin/dashboard"
      case "employee":
        return "/employee/dashboard"
      default:
        return "/"
    }
  }
  
  return (
    <nav className="sticky top-0 z-50 bg-white dark:bg-slate-900 border-b border-slate-200 dark:border-slate-700 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Left side - Logo and title matching reference */}
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate(getHomeRoute())}
              className="group flex items-center space-x-3 hover:scale-105 transition-all duration-300"
            >
              {/* Logo matching reference design */}
              <div className="w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center shadow-md group-hover:shadow-lg transition-all duration-300">
                <Icon name="tasks" size="sm" className="text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-slate-900 dark:text-white">
                  Task Tracker
                </h1>
              </div>
            </button>
          </div>
          
          {/* Right side - User menu matching reference */}
          <div className="flex items-center space-x-4">
            {/* Theme toggle */}
            <ThemeToggle />

            {/* Notifications */}
            <Button variant="ghost" size="sm" className="relative">
              <Icon name="notifications" size="sm" />
              <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">
                3
              </span>
            </Button>

            {/* User Profile Dropdown */}
            {user && (
              <div className="relative">
                <button className="flex items-center space-x-3 hover:bg-slate-50 dark:hover:bg-slate-800 rounded-lg px-3 py-2 transition-colors">
                  <div className="text-right hidden sm:block">
                    <p className="text-sm font-medium text-slate-900 dark:text-white">
                      {user.name || "Elena Perera"}
                    </p>
                    <p className="text-xs text-slate-500 dark:text-slate-400">
                      <EMAIL>
                    </p>
                  </div>
                  <Avatar
                    src={user.avatar}
                    alt={user.name || "Elena Perera"}
                    size="sm"
                    className="ring-2 ring-slate-200 dark:ring-slate-700"
                  />
                  <Icon name="chevron-down" size="xs" className="text-slate-400" />
                </button>

                {/* Dropdown menu would go here */}
              </div>
            )}

            {/* Mobile menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d={isMenuOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16M4 18h16"}
                />
              </svg>
            </button>
            
            {/* Logout button */}
            {user && (
              <Button
                variant="glass"
                size="sm"
                onClick={logout}
                className="hidden sm:flex backdrop-blur-sm hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-600 dark:hover:text-red-400 transition-colors"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                  />
                </svg>
                Logout
              </Button>
            )}
          </div>
        </div>
        
        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-200 dark:border-gray-700">
            <div className="space-y-2">
              {actions.map((action, index) => (
                <Button
                  key={index}
                  variant={action.variant || "ghost"}
                  size="sm"
                  onClick={() => {
                    action.onClick()
                    setIsMenuOpen(false)
                  }}
                  className={cn("w-full justify-start", action.className)}
                >
                  {action.icon && <span className="mr-2">{action.icon}</span>}
                  {action.label}
                </Button>
              ))}
              
              {user && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    logout()
                    setIsMenuOpen(false)
                  }}
                  className="w-full justify-start"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                    />
                  </svg>
                  Logout
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}

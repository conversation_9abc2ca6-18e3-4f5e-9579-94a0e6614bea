import { useTheme } from "../../contexts/ThemeContext"
import { Button } from "./Button"
import { Icon } from "./Icons"

export function ThemeToggle({ className, variant = "ghost", size = "default" }) {
  const { isDark, toggleTheme } = useTheme()
  
  return (
    <Button
      variant={variant}
      size={size}
      onClick={toggleTheme}
      className={className}
      aria-label={isDark ? "Switch to light mode" : "Switch to dark mode"}
    >
      <div className="relative w-5 h-5">
        {/* Sun Icon */}
        <Icon
          name="sun"
          size="sm"
          className={`absolute inset-0 transition-all duration-500 ${
            isDark ? "rotate-90 scale-0 opacity-0" : "rotate-0 scale-100 opacity-100"
          }`}
        />

        {/* Moon Icon */}
        <Icon
          name="moon"
          size="sm"
          className={`absolute inset-0 transition-all duration-500 ${
            isDark ? "rotate-0 scale-100 opacity-100" : "-rotate-90 scale-0 opacity-0"
          }`}
        />
      </div>
    </Button>
  )
}

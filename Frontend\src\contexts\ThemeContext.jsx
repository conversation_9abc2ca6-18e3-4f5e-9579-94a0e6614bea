import { createContext, useContext, useState, useEffect } from "react"

const ThemeContext = createContext()

export const useTheme = () => {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error("useTheme must be used within a ThemeProvider")
  }
  return context
}

// Theme configuration based on reference images
const themes = {
  light: {
    name: "light",
    colors: {
      // Primary brand colors
      primary: "99 102 241", // indigo-500 - main brand color
      primaryLight: "129 140 248", // indigo-400
      primaryDark: "79 70 229", // indigo-600

      // Status colors matching reference
      success: "34 197 94", // green-500 - completed tasks
      successLight: "74 222 128", // green-400
      warning: "245 158 11", // amber-500 - pending tasks
      warningLight: "251 191 36", // amber-400
      danger: "239 68 68", // red-500 - overdue/urgent
      dangerLight: "248 113 113", // red-400
      info: "168 85 247", // purple-500 - other metrics
      infoLight: "196 181 253", // purple-300

      // Background system
      background: "255 255 255", // pure white
      backgroundSecondary: "248 250 252", // slate-50
      backgroundTertiary: "241 245 249", // slate-100
      backgroundElevated: "255 255 255", // white for cards

      // Text hierarchy
      foreground: "15 23 42", // slate-900 - primary text
      foregroundSecondary: "51 65 85", // slate-700
      foregroundTertiary: "100 116 139", // slate-500
      foregroundMuted: "148 163 184", // slate-400

      // Border system
      border: "226 232 240", // slate-200
      borderSecondary: "203 213 225", // slate-300
      borderInteractive: "99 102 241", // indigo-500

      // Card backgrounds for different statuses
      cardBlue: "239 246 255", // blue-50
      cardGreen: "240 253 244", // green-50
      cardOrange: "255 251 235", // amber-50
      cardPurple: "250 245 255", // purple-50
      cardGray: "248 250 252", // slate-50
    }
  },
  dark: {
    name: "dark",
    colors: {
      // Primary brand colors (adjusted for dark theme)
      primary: "129 140 248", // indigo-400 - better contrast in dark
      primaryLight: "165 180 252", // indigo-300
      primaryDark: "99 102 241", // indigo-500

      // Status colors for dark theme
      success: "74 222 128", // green-400
      successLight: "134 239 172", // green-300
      warning: "251 191 36", // amber-400
      warningLight: "252 211 77", // amber-300
      danger: "248 113 113", // red-400
      dangerLight: "252 165 165", // red-300
      info: "196 181 253", // purple-300
      infoLight: "221 214 254", // purple-200

      // Dark background system
      background: "15 23 42", // slate-900 - main dark background
      backgroundSecondary: "30 41 59", // slate-800
      backgroundTertiary: "51 65 85", // slate-700
      backgroundElevated: "30 41 59", // slate-800 for cards

      // Dark text hierarchy
      foreground: "248 250 252", // slate-50 - primary text
      foregroundSecondary: "226 232 240", // slate-200
      foregroundTertiary: "148 163 184", // slate-400
      foregroundMuted: "100 116 139", // slate-500

      // Dark border system
      border: "51 65 85", // slate-700
      borderSecondary: "71 85 105", // slate-600
      borderInteractive: "129 140 248", // indigo-400

      // Dark card backgrounds
      cardBlue: "30 58 138", // blue-900/20
      cardGreen: "20 83 45", // green-900/20
      cardOrange: "146 64 14", // amber-900/20
      cardPurple: "88 28 135", // purple-900/20
      cardGray: "30 41 59", // slate-800
    }
  }
}

export const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState(() => {
    // Check for saved theme preference or default to system preference
    const saved = localStorage.getItem("theme")
    if (saved && (saved === "light" || saved === "dark")) {
      return saved
    }

    // Check system preference
    if (typeof window !== "undefined" && window.matchMedia) {
      return window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light"
    }

    return "light"
  })

  const isDark = theme === "dark"
  const currentTheme = themes[theme]

  useEffect(() => {
    // Save theme preference
    localStorage.setItem("theme", theme)

    // Apply theme to document
    const root = document.documentElement

    if (theme === "dark") {
      root.classList.add("dark")
    } else {
      root.classList.remove("dark")
    }

    // Apply CSS custom properties
    Object.entries(currentTheme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value)
    })

    // Update meta theme-color for mobile browsers
    const metaThemeColor = document.querySelector('meta[name="theme-color"]')
    if (metaThemeColor) {
      metaThemeColor.setAttribute("content", theme === "dark" ? "#1f2937" : "#ffffff")
    }
  }, [theme, currentTheme])

  // Listen for system theme changes
  useEffect(() => {
    if (typeof window === "undefined" || !window.matchMedia) return

    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)")
    const handleChange = (e) => {
      // Only auto-switch if user hasn't manually set a preference
      const saved = localStorage.getItem("theme")
      if (!saved) {
        setTheme(e.matches ? "dark" : "light")
      }
    }

    mediaQuery.addEventListener("change", handleChange)
    return () => mediaQuery.removeEventListener("change", handleChange)
  }, [])

  const toggleTheme = () => {
    setTheme(prev => prev === "dark" ? "light" : "dark")
  }

  const setLightTheme = () => setTheme("light")
  const setDarkTheme = () => setTheme("dark")

  const value = {
    theme,
    isDark,
    currentTheme,
    toggleTheme,
    setLightTheme,
    setDarkTheme,
    setTheme
  }

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
}

@import "tailwindcss";

/* Custom CSS Variables for Enhanced Modern Theming */
:root {
  /* Modern Light Theme Color Palette */
  --color-primary: 99 102 241; /* indigo-500 - Modern primary */
  --color-primary-light: 129 140 248; /* indigo-400 */
  --color-primary-dark: 79 70 229; /* indigo-600 */
  --color-primary-darker: 67 56 202; /* indigo-700 */

  --color-secondary: 34 197 94; /* green-500 - Success/secondary */
  --color-secondary-light: 74 222 128; /* green-400 */
  --color-secondary-dark: 22 163 74; /* green-600 */

  --color-accent: 236 72 153; /* pink-500 - Accent color */
  --color-accent-light: 244 114 182; /* pink-400 */
  --color-accent-dark: 219 39 119; /* pink-600 */

  --color-warning: 245 158 11; /* amber-500 */
  --color-warning-light: 251 191 36; /* amber-400 */
  --color-warning-dark: 217 119 6; /* amber-600 */

  --color-danger: 239 68 68; /* red-500 */
  --color-danger-light: 248 113 113; /* red-400 */
  --color-danger-dark: 220 38 38; /* red-600 */

  /* Modern Background System */
  --color-bg-primary: 255 255 255; /* Pure white */
  --color-bg-secondary: 248 250 252; /* slate-50 - Subtle background */
  --color-bg-tertiary: 241 245 249; /* slate-100 - Card backgrounds */
  --color-bg-quaternary: 226 232 240; /* slate-200 - Subtle borders */
  --color-bg-elevated: 255 255 255; /* White with shadow for cards */

  /* Modern Text Hierarchy */
  --color-text-primary: 15 23 42; /* slate-900 - Main text */
  --color-text-secondary: 51 65 85; /* slate-700 - Secondary text */
  --color-text-tertiary: 100 116 139; /* slate-500 - Muted text */
  --color-text-quaternary: 148 163 184; /* slate-400 - Placeholder text */
  --color-text-inverse: 255 255 255; /* White text for dark backgrounds */

  /* Modern Border System */
  --color-border-primary: 226 232 240; /* slate-200 - Main borders */
  --color-border-secondary: 203 213 225; /* slate-300 - Stronger borders */
  --color-border-tertiary: 148 163 184; /* slate-400 - Focus borders */
  --color-border-interactive: 99 102 241; /* indigo-500 - Interactive borders */

  /* Enhanced Shadow System */
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);

  /* Modern Glassmorphism */
  --glass-bg: rgba(255, 255, 255, 0.85);
  --glass-bg-strong: rgba(255, 255, 255, 0.95);
  --glass-border: rgba(255, 255, 255, 0.3);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
  --glass-backdrop: blur(16px);

  /* Modern Gradients */
  --gradient-primary: linear-gradient(135deg, rgb(99 102 241) 0%, rgb(129 140 248) 100%);
  --gradient-secondary: linear-gradient(135deg, rgb(34 197 94) 0%, rgb(74 222 128) 100%);
  --gradient-accent: linear-gradient(135deg, rgb(236 72 153) 0%, rgb(244 114 182) 100%);
  --gradient-warm: linear-gradient(135deg, rgb(251 191 36) 0%, rgb(245 158 11) 100%);
  --gradient-cool: linear-gradient(135deg, rgb(56 189 248) 0%, rgb(99 102 241) 100%);
  --gradient-surface: linear-gradient(135deg, rgb(248 250 252) 0%, rgb(255 255 255) 100%);

  /* Animation System */
  --duration-instant: 0ms;
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;
  --duration-slower: 500ms;
  --ease-out: cubic-bezier(0.16, 1, 0.3, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

  /* Modern Border Radius System */
  --radius-xs: 0.25rem; /* 4px */
  --radius-sm: 0.375rem; /* 6px */
  --radius-md: 0.5rem; /* 8px */
  --radius-lg: 0.75rem; /* 12px */
  --radius-xl: 1rem; /* 16px */
  --radius-2xl: 1.5rem; /* 24px */
  --radius-3xl: 2rem; /* 32px */
  --radius-full: 9999px;

  /* Spacing System */
  --space-xs: 0.25rem; /* 4px */
  --space-sm: 0.5rem; /* 8px */
  --space-md: 1rem; /* 16px */
  --space-lg: 1.5rem; /* 24px */
  --space-xl: 2rem; /* 32px */
  --space-2xl: 3rem; /* 48px */
  --space-3xl: 4rem; /* 64px */
}

/* Modern Dark Theme - Matching Reference Design */
.dark {
  /* Dark Theme Background System - Deep navy like reference */
  --color-bg-primary: 15 23 42; /* slate-900 - Main dark background like reference */
  --color-bg-secondary: 30 41 59; /* slate-800 - Card backgrounds */
  --color-bg-tertiary: 51 65 85; /* slate-700 - Elevated surfaces */
  --color-bg-quaternary: 71 85 105; /* slate-600 - Interactive elements */
  --color-bg-elevated: 30 41 59; /* slate-800 with shadow for cards */

  /* Dark Theme Text Hierarchy */
  --color-text-primary: 248 250 252; /* slate-50 - Primary text */
  --color-text-secondary: 226 232 240; /* slate-200 - Secondary text */
  --color-text-tertiary: 148 163 184; /* slate-400 - Muted text */
  --color-text-quaternary: 100 116 139; /* slate-500 - Placeholder text */
  --color-text-inverse: 15 23 42; /* Dark text for light backgrounds */

  /* Dark Theme Border System */
  --color-border-primary: 51 65 85; /* slate-700 - Main borders */
  --color-border-secondary: 71 85 105; /* slate-600 - Stronger borders */
  --color-border-tertiary: 100 116 139; /* slate-500 - Focus borders */
  --color-border-interactive: 129 140 248; /* indigo-400 - Interactive borders */

  /* Enhanced Dark Shadows */
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.4), 0 1px 2px -1px rgb(0 0 0 / 0.4);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.5), 0 8px 10px -6px rgb(0 0 0 / 0.5);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.6);
  --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.3);

  /* Dark Glassmorphism */
  --glass-bg: rgba(15, 23, 42, 0.85);
  --glass-bg-strong: rgba(15, 23, 42, 0.95);
  --glass-border: rgba(148, 163, 184, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.5);
  --glass-backdrop: blur(16px);

  /* Dark Theme Gradients */
  --gradient-primary: linear-gradient(135deg, rgb(129 140 248) 0%, rgb(99 102 241) 100%);
  --gradient-secondary: linear-gradient(135deg, rgb(74 222 128) 0%, rgb(34 197 94) 100%);
  --gradient-accent: linear-gradient(135deg, rgb(244 114 182) 0%, rgb(236 72 153) 100%);
  --gradient-warm: linear-gradient(135deg, rgb(251 191 36) 0%, rgb(245 158 11) 100%);
  --gradient-cool: linear-gradient(135deg, rgb(56 189 248) 0%, rgb(129 140 248) 100%);
  --gradient-surface: linear-gradient(135deg, rgb(15 23 42) 0%, rgb(30 41 59) 100%);

  /* Adjust primary colors for better dark theme contrast */
  --color-primary-light: 165 180 252; /* indigo-300 */
  --color-secondary-light: 134 239 172; /* green-300 */
  --color-accent-light: 249 168 212; /* pink-300 */
  --color-warning-light: 252 211 77; /* amber-300 */
  --color-danger-light: 252 165 165; /* red-300 */
}

/* Global Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color var(--duration-normal) ease, color var(--duration-normal) ease;
}

/* Modern Enhanced Component Classes */
.glass-card {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  border-radius: var(--radius-xl);
  transition: all var(--duration-normal) var(--ease-out);
}

.glass-card-strong {
  background: var(--glass-bg-strong);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  box-shadow: var(--shadow-xl);
  border-radius: var(--radius-xl);
  transition: all var(--duration-normal) var(--ease-out);
}

.modern-card {
  background: rgb(var(--color-bg-elevated));
  border: 1px solid rgb(var(--color-border-primary));
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  transition: all var(--duration-normal) var(--ease-out);
}

.modern-card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-2px);
  border-color: rgb(var(--color-border-interactive) / 0.3);
}

.gradient-card {
  background: var(--gradient-surface);
  border: 1px solid rgb(var(--color-border-primary) / 0.5);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  transition: all var(--duration-normal) var(--ease-out);
}

.floating-card {
  background: rgb(var(--color-bg-elevated));
  border: 1px solid rgb(var(--color-border-primary) / 0.5);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  transition: all var(--duration-normal) var(--ease-out);
}

.floating-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 32px 64px -12px rgb(0 0 0 / 0.15);
}

.glass-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.btn-primary {
  background: linear-gradient(135deg, rgb(var(--color-primary)) 0%, rgb(var(--color-primary-dark)) 100%);
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-lg);
  border: none;
  cursor: pointer;
  transition: all var(--duration-normal) ease;
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

/* Modern Utility Classes */
.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

.bg-gradient-primary {
  background: var(--gradient-primary);
}

.bg-gradient-secondary {
  background: var(--gradient-secondary);
}

.bg-gradient-accent {
  background: var(--gradient-accent);
}

.bg-gradient-warm {
  background: var(--gradient-warm);
}

.bg-gradient-cool {
  background: var(--gradient-cool);
}

.bg-gradient-surface {
  background: var(--gradient-surface);
}

.modern-shadow {
  box-shadow: var(--shadow-lg);
}

.modern-shadow-xl {
  box-shadow: var(--shadow-xl);
}

.modern-shadow-2xl {
  box-shadow: var(--shadow-2xl);
}

.modern-border {
  border: 1px solid rgb(var(--color-border-primary));
}

.modern-border-interactive {
  border: 1px solid rgb(var(--color-border-interactive) / 0.3);
}

.modern-focus {
  outline: 2px solid rgb(var(--color-primary) / 0.5);
  outline-offset: 2px;
}

.interactive-scale {
  transition: transform var(--duration-normal) var(--ease-out);
}

.interactive-scale:hover {
  transform: scale(1.02);
}

.interactive-lift {
  transition: all var(--duration-normal) var(--ease-out);
}

.interactive-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.interactive-glow {
  transition: all var(--duration-normal) var(--ease-out);
}

.interactive-glow:hover {
  box-shadow: 0 0 20px rgb(var(--color-primary) / 0.3);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--duration-slow) ease;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-secondary {
  background: rgb(var(--color-bg-primary));
  color: rgb(var(--color-text-primary));
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-lg);
  border: 2px solid rgb(var(--color-border-primary));
  cursor: pointer;
  transition: all var(--duration-normal) ease;
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background: rgb(var(--color-bg-secondary));
  border-color: rgb(var(--color-primary));
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.card {
  background: rgb(var(--color-bg-primary));
  border: 1px solid rgb(var(--color-border-primary));
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  transition: all var(--duration-normal) ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.input-field {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid rgb(var(--color-border-primary));
  border-radius: var(--radius-lg);
  background: rgb(var(--color-bg-primary));
  color: rgb(var(--color-text-primary));
  font-size: 1rem;
  transition: all var(--duration-normal) ease;
}

.input-field:focus {
  outline: none;
  border-color: rgb(var(--color-primary));
  box-shadow: 0 0 0 3px rgba(var(--color-primary), 0.1);
}

.input-field::placeholder {
  color: rgb(var(--color-text-tertiary));
}

/* Loading Animation */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgb(var(--color-border-primary));
  border-top: 2px solid rgb(var(--color-primary));
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes fade-in {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slide-in-right {
  from { opacity: 0; transform: translateX(20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes slide-in-left {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes scale-in {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 5px rgb(var(--color-primary) / 0.3); }
  50% { box-shadow: 0 0 20px rgb(var(--color-primary) / 0.6); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
}

/* Animation Classes */
.animate-fade-in {
  animation: fade-in 0.5s var(--ease-out) forwards;
}

.animate-slide-in-right {
  animation: slide-in-right 0.5s var(--ease-out) forwards;
}

.animate-slide-in-left {
  animation: slide-in-left 0.5s var(--ease-out) forwards;
}

.animate-scale-in {
  animation: scale-in 0.3s var(--ease-out) forwards;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Gradient Backgrounds */
.gradient-bg-primary {
  background: linear-gradient(135deg, rgb(var(--color-primary)) 0%, rgb(var(--color-accent)) 100%);
}

.gradient-bg-secondary {
  background: linear-gradient(135deg, rgb(var(--color-secondary)) 0%, rgb(var(--color-secondary-dark)) 100%);
}

/* Smooth Transitions */
.transition-all {
  transition: all var(--duration-normal) ease;
}

.transition-fast {
  transition: all var(--duration-fast) ease;
}

.transition-slow {
  transition: all var(--duration-slow) ease;
}
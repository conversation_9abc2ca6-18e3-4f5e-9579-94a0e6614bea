import { useState } from "react"
import { useAuth } from "../contexts/AuthContext"
import { useTheme } from "../contexts/ThemeContext"
import { Navigation } from "../components/ui/Navigation"
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "../components/ui/Card"
import { Button } from "../components/ui/Button"
import { Badge } from "../components/ui/Badge"
import { Avatar } from "../components/ui/Avatar"
import { Icon, MetricIcon, TaskStatusIcon } from "../components/ui/Icons"

export default function EmployeeDashboard() {
  const { user } = useAuth()
  const { currentTheme } = useTheme()
  const [activeTab, setActiveTab] = useState("overview")

  // Mock data for demonstration - Enhanced with progress tracking
  const stats = {
    totalTasks: 3,
    completedTasks: 1,
    pendingTasks: 2,
    hoursLogged: 0
  }

  const recentTasks = [
    {
      id: 1,
      title: "hfvd",
      description: "csdtci qvqjcjuh",
      status: "pending",
      priority: "medium",
      dueDate: "Tuesday, June 24, 2025",
      estimatedHours: 1.9,
      actualHours: 1.5,
      progress: 79,
      isOvertime: false
    },
    {
      id: 2,
      title: "task-example",
      description: "task task task",
      status: "completed",
      priority: "high",
      dueDate: "Wednesday, June 25, 2025",
      estimatedHours: 1.9,
      actualHours: 1.3,
      progress: 100,
      isOvertime: true,
      overtimeHours: 2.0
    },
    {
      id: 3,
      title: "dlkhrf",
      description: "c4hxyr4r fxjhyc",
      status: "pending",
      priority: "medium",
      dueDate: "Thursday, June 26, 2025",
      estimatedHours: 2.0,
      actualHours: 2.9,
      progress: 145,
      isOvertime: true,
      overtimeHours: 0.9
    }
  ]

  const navigationActions = [
    {
      label: "My Tasks",
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
        </svg>
      ),
      onClick: () => setActiveTab("tasks"),
      variant: activeTab === "tasks" ? "primary" : "ghost"
    },
    {
      label: "Time Tracking",
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      onClick: () => setActiveTab("time"),
      variant: activeTab === "time" ? "primary" : "ghost"
    },
    {
      label: "Profile",
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      ),
      onClick: () => setActiveTab("profile"),
      variant: activeTab === "profile" ? "primary" : "ghost"
    }
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case "completed": return "success"
      case "in-progress": return "warning"
      case "pending": return "default"
      default: return "default"
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "urgent": return "danger"
      case "high": return "warning"
      case "medium": return "info"
      case "low": return "default"
      default: return "default"
    }
  }

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900">
      <Navigation />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8">
        {/* Welcome Section matching reference */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-slate-900 dark:text-white mb-2">
            Welcome back, {user?.name || "Elena"}! 👋
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            Here's what's happening with your tasks today.
          </p>
        </div>

        {/* Stats Overview - Redesigned to match reference */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Total Tasks Card - Blue */}
          <Card className="group bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-slate-600 dark:text-slate-400 mb-2">Total Tasks</p>
                  <p className="text-3xl font-bold text-slate-900 dark:text-white">{stats.totalTasks}</p>
                </div>
                <div className="w-14 h-14 bg-blue-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform">
                  <MetricIcon type="total" size="lg" className="text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Completed Tasks Card - Green */}
          <Card className="group bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-slate-600 dark:text-slate-400 mb-2">Completed</p>
                  <p className="text-3xl font-bold text-slate-900 dark:text-white">{stats.completedTasks}</p>
                </div>
                <div className="w-14 h-14 bg-green-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform">
                  <MetricIcon type="completed" size="lg" className="text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Pending Tasks Card - Orange */}
          <Card className="group bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-slate-600 dark:text-slate-400 mb-2">Pending</p>
                  <p className="text-3xl font-bold text-slate-900 dark:text-white">{stats.pendingTasks}</p>
                </div>
                <div className="w-14 h-14 bg-orange-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform">
                  <MetricIcon type="pending" size="lg" className="text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Hours Logged Card - Purple */}
          <Card className="group bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-slate-600 dark:text-slate-400 mb-2">Hours Logged</p>
                  <p className="text-3xl font-bold text-slate-900 dark:text-white">{stats.hoursLogged}</p>
                </div>
                <div className="w-14 h-14 bg-purple-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform">
                  <MetricIcon type="time" size="lg" className="text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Area - Task List matching reference design */}
        <div className="space-y-6">
          {/* Header with controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Icon name="tasks" size="lg" className="text-purple-500" />
                <h2 className="text-2xl font-bold text-slate-900 dark:text-white">All Tasks</h2>
              </div>
              <span className="text-sm text-slate-500 dark:text-slate-400">{stats.totalTasks} tasks</span>
              <Button variant="outline" size="sm" className="ml-auto">
                <Icon name="export" size="sm" className="mr-2" />
                Export
              </Button>
            </div>
          </div>

          {/* Task List */}
          <div className="space-y-6">
            {/* Group by date */}
            {Object.entries(
              recentTasks.reduce((groups, task) => {
                const date = task.dueDate;
                if (!groups[date]) groups[date] = [];
                groups[date].push(task);
                return groups;
              }, {})
            ).map(([date, tasks]) => (
              <div key={date} className="space-y-4">
                {/* Date Header */}
                <div className="flex items-center space-x-3">
                  <Icon name="calendar" size="sm" className="text-slate-400" />
                  <h3 className="text-sm font-medium text-slate-900 dark:text-white">{date}</h3>
                  <span className="text-xs text-slate-500">({tasks.length} task{tasks.length !== 1 ? 's' : ''})</span>
                </div>

                {/* Tasks for this date */}
                <div className="space-y-3">
                  {tasks.map((task) => (
                    <Card key={task.id} className="group bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 hover:shadow-lg transition-all duration-300">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between">
                          {/* Task Info */}
                          <div className="flex items-start space-x-4 flex-1">
                            {/* Status Checkbox */}
                            <div className="mt-1">
                              <div className={`w-5 h-5 rounded border-2 flex items-center justify-center ${
                                task.status === 'completed'
                                  ? 'bg-green-500 border-green-500'
                                  : 'border-slate-300 dark:border-slate-600'
                              }`}>
                                {task.status === 'completed' && (
                                  <Icon name="check-circle" size="xs" className="text-white" />
                                )}
                              </div>
                            </div>

                            {/* Task Details */}
                            <div className="flex-1 min-w-0">
                              <h4 className="font-semibold text-slate-900 dark:text-white mb-1">{task.title}</h4>
                              <p className="text-sm text-slate-600 dark:text-slate-400 mb-3">{task.description}</p>

                              {/* Progress Bar */}
                              <div className="mb-3">
                                <div className="flex items-center justify-between text-xs mb-1">
                                  <span className="text-slate-600 dark:text-slate-400">Progress</span>
                                  <span className="text-slate-900 dark:text-white font-medium">
                                    {task.actualHours}h / {task.estimatedHours}h ({task.progress}%)
                                    {task.isOvertime && (
                                      <span className="text-red-500 ml-1">(+{task.overtimeHours}h overtime)</span>
                                    )}
                                  </span>
                                </div>
                                <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                                  <div
                                    className={`h-2 rounded-full transition-all duration-300 ${
                                      task.progress >= 100
                                        ? task.isOvertime
                                          ? 'bg-gradient-to-r from-green-500 to-red-500'
                                          : 'bg-green-500'
                                        : 'bg-blue-500'
                                    }`}
                                    style={{ width: `${Math.min(task.progress, 100)}%` }}
                                  />
                                  {task.progress > 100 && (
                                    <div
                                      className="h-2 bg-red-500 rounded-r-full -mt-2"
                                      style={{ width: `${Math.min(task.progress - 100, 50)}%`, marginLeft: '100%' }}
                                    />
                                  )}
                                </div>
                                {task.isOvertime && (
                                  <div className="flex items-center mt-1">
                                    <Icon name="warning" size="xs" className="text-amber-500 mr-1" />
                                    <span className="text-xs text-amber-600 dark:text-amber-400">
                                      {task.overtimeHours} hours over estimate
                                    </span>
                                    {task.status !== 'completed' && (
                                      <span className="text-xs text-red-600 dark:text-red-400 ml-2">
                                        ✓ Ready for completion!
                                      </span>
                                    )}
                                  </div>
                                )}
                              </div>

                              {/* Time and Status */}
                              <div className="flex items-center space-x-4 text-xs">
                                <div className="flex items-center space-x-1">
                                  <Icon name="clock" size="xs" className="text-slate-400" />
                                  <span className="text-slate-600 dark:text-slate-400">Est: {task.estimatedHours}h</span>
                                </div>
                                <Badge
                                  variant={task.status === 'completed' ? 'success' : task.status === 'pending' ? 'warning' : 'default'}
                                  size="sm"
                                >
                                  {task.status === 'completed' ? 'Completed' : 'Pending'}
                                </Badge>
                              </div>
                            </div>
                          </div>

                          {/* Action Buttons */}
                          <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                            <Button variant="ghost" size="sm">
                              <Icon name="plus" size="sm" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Icon name="eye" size="sm" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Icon name="edit" size="sm" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Icon name="delete" size="sm" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </main>
    </div>
  )
}
